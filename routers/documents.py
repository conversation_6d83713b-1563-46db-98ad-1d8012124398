from fastapi import APIRouter, Request
from pydantic import BaseModel
import requests
from services.documents import Documents
from utils.collections import generate_collection_id
from utils.logger import logger

documents_router = APIRouter(
    prefix="/documents",
    tags=["documents"],
    responses={404: {"description": "Not found"}},
)

documentService = Documents()

class DocumentToVectorise(BaseModel, extra="allow"):
    document_content: bytes | None
    file_type: str
    document_embedding: str

def get_document(document: str) -> DocumentToVectorise | None:
    file_type = documentService._get_file_type(document)
    if not file_type or type(file_type) is not str:
        return None
    downloaded_content = documentService.download_document(document)
    if not downloaded_content or type(downloaded_content) is not bytes:
        return None
    resp =  DocumentToVectorise(
        document_content= downloaded_content,
        file_type=file_type,
        document_embedding = '',
    )
    return resp

def load_results(context: Request):
    documents_to_download = documentService.get_documents()
    documents_map: dict[str, list[DocumentToVectorise]] = {}
    

    for each_document_to_download in documents_to_download:
        documents_map[f"{each_document_to_download["tenant_id"]}_{each_document_to_download["role"]}"] = list(
            filter(None, map(get_document, each_document_to_download["documents"])))
        
    for collection_key, documents in documents_map.items():
        if len(documents) == 0:
            pass
        documents_to_push_to_vectordb = []
        for document in documents:
            document_content, file_type = document.document_content, document.file_type
            if not document_content or type(document_content) is not bytes or not file_type or type(file_type) is not str:
                raise requests.exceptions.RequestException(f"Failed to download document: {file_type}")
            text_content = documentService.generate_embeddings(document_content, file_type)
            documents_to_push_to_vectordb.append(text_content)
        
        success, error = context.app.state.vector_storage.create_chroma_db(documents_to_push_to_vectordb, generate_collection_id(collection_key))
        if success:
            logger.info("Chroma DB created successfully: %s with hash %s", collection_key, generate_collection_id(collection_key))
            continue
        else:
            logger.error("Error creating Chroma DB: %s", error, collection_key)
        
    return True

@documents_router.post("/load")
async def load_documents(context: Request):
    return {"message": "Document embeddings loaded successfully."} if load_results(context) else {"message": "Failed to load document embeddings."}
