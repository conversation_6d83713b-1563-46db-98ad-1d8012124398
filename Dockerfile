FROM python:3.13-slim

RUN apt-get update && apt-get install -y \
    build-essential \
    cmake \
    && apt-get clean

RUN pip install --no-cache-dir poetry==2.1.2

RUN pip install "fastapi[standard]"


WORKDIR /app

COPY . .

RUN poetry config virtualenvs.create false
RUN poetry install --no-root

EXPOSE 8000

CMD ["poetry", "run", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--no-access-log"]

