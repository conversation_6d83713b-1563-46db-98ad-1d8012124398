name: build and push hrms-core project 
on:
  push:
    branches:
      - release-candidate
      - production-release
      - feautre-natural-chat

jobs:
  build-and-push:
    name: Build and Push to ECR
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v2

    - name: Configure AWS Credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ap-south-1 

    - name: Login To ECR Repo
      id: login-ecr
      run: |
        aws ecr get-login-password --region ap-south-1 | docker login --username AWS --password-stdin 459854451213.dkr.ecr.ap-south-1.amazonaws.com

    - name: Set Up Git Tags
      id: setup-tags
      run: |
        BRANCH_NAME="${GITHUB_REF#refs/heads/}"
        DATE=$(date +%Y%m%d)
        TIME=$(date +%H%M)
        TAG_NAME="${BRANCH_NAME}-${DATE}-${TIME}"
        echo "TAG_NAME=$TAG_NAME" >> $GITHUB_ENV

    - name: Determine Dockerfile
      id: determine-dockerfile
      run: |
        echo "DOCKERFILE_NAME=Dockerfile" >> $GITHUB_ENV

    - name: Display Tag Name
      run: |
        echo "Image has been tagged with: ${{ env.TAG_NAME }}"

    - name: Build, Tag, and Push to ECR
      env: 
        ECR_REGISTRY: 459854451213.dkr.ecr.ap-south-1.amazonaws.com
        ECR_REPOSITORY: effi-rag
        IMAGE_TAG: ${{ env.TAG_NAME }}
        DOCKERFILE_NAME: ${{ env.DOCKERFILE_NAME }}
      run: |
        docker build -f $DOCKERFILE_NAME -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG

    - name: Display Tag Name
      run: |
        echo "Image has been tagged with: ${{ env.TAG_NAME }}"
