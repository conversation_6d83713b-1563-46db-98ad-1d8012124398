
from email import message
from chromadb import Client, errors as chroma_errors
from services.geminiai import GeminiEmbeddingFunction
from utils.logger import logger


class VectorDb: 
    def __init__(self):
        self.chroma_client = Client()
    
    def create_chroma_db(self, documents: list[str], collection_key: str):
        try:
            collection = self.chroma_client.get_or_create_collection(
                    name=collection_key,
                    embedding_function=GeminiEmbeddingFunction()
                )
            
            for i, d in enumerate(documents):
                collection.add(
                    documents=[d],
                    ids=[str(i)],
                )
            return { message: f"Chroma Collection created successfully {collection.name}" }, None
        except Exception as e:
            logger.error("Error creating Chroma DB: %s", e)
            return None, { message: "<PERSON>rror creating Chroma DB" }
        
    def get_chroma_db(self, collection_id: str):
        try:
            logger.debug(f"Getting from db {collection_id}")
            db = self.chroma_client.get_collection(
                name=collection_id,
                embedding_function=GeminiEmbeddingFunction()
            )
            logger.debug(f"Collection name {db
                         .name}")
        except chroma_errors.NotFoundError:
            logger.error("Collection not found")
            return None
        except Exception as e:
            logger.error("Error getting Chroma DB: %s", e)
            return None
        return db
    
    def get_relevant_passage_from_db(self, query, db):
        passage = db.query(query_texts=[query], n_results=1)
        # logger.info("Passage: %s", passage)
        return passage['documents'][0][0] if passage['documents'][0] else None