from enum import Enum
import uuid
from dataclasses import dataclass
import datetime
import pytz
from dotenv import load_dotenv
import os
import yaml
from utils.logger import logger

CONFIG_DIR = "config" # Assuming config folder is at the project root
AGENT_PROFILE_PATH = os.path.join(CONFIG_DIR, "agent_profile.yaml")
AGENT_PROMPTS_PATH = os.path.join(CONFIG_DIR, "agent_prompts.yaml")
APP_SETTINGS_PATH = os.path.join(CONFIG_DIR, "app_settings.yaml")

def load_yaml_config(file_path: str):
    """Loads configuration from a YAML file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except FileNotFoundError:
        logger.error(f"Error: Configuration file '{file_path}' not found.")
        raise
    except yaml.YAMLError as exc:
        logger.error(f"Error parsing YAML file '{file_path}': {exc}")
        raise

agent_profile = load_yaml_config(AGENT_PROFILE_PATH)
agent_prompts = load_yaml_config(AGENT_PROMPTS_PATH)
app_settings = load_yaml_config(APP_SETTINGS_PATH)

class ChatMessageUserTypes(Enum):
    USER = "user"
    ASSISTANT = "assistant"

@dataclass
class ChatMessage:
    type: ChatMessageUserTypes
    content: str

class Chat:
    def __init__(self):
        self.chat_id = uuid.uuid4()
        self.messages = {}

    def add_message(self, message: ChatMessage):
        """_summary_

        Args:
            message (ChatMessage): _description_
        """
        previous_messages: list[ChatMessage] = self.messages.get(self.chat_id, [])
        previous_messages.append(message)
        self.messages[self.chat_id] = previous_messages

    def get_messages(self):
        """_summary_

        Returns:
            _type_: _description_
        """
        return self.messages
    
    # def make_prompt(self, query: str, relevant_passage: str | None) -> str:
    #     if (relevant_passage is None):
    #         relevant_passage = "No relevant passage found"
    #     escaped = relevant_passage.replace("'", "").replace('"', "").replace("\n", " ")
    #     #  You are a helpful HRMS assistant for effiHR tenants. 
    #     #     You will be fed with a query and a relevant passage from the documents which might be of the company/organisation/each users. 
    #     #     You should give relevant answers to the query based on the passage.
    #     #     You should not give any other information apart from the passage.
    #     #     You should not give any information which is not present in the passage. 
    #     #     You should not give any information which is not related to the query. 
    #     #     You should not give any information which is not related to the company/organisation/each users.
    #     #     You should not give any information which is not related to the HRMS.
    #     #     You should not give any information which is not related to the HRMS. 
    #     #     You should not give any information which is not related to the company/organisation/each users. 
    #     #     You should not give any information which is not related to the HRMS. 
    #     #     You should not give any information which is not related to the company/organisation/each users. 
    #     #     You should not give any information which is not related to the HRMS. 
    #     #     You should not give any information which is not related to the company/organisation/each users. 
    #     #     You should not give any information which is not related to the HRMS. 
    #     #     You should not give any information which is not related to the company/organisation/each users. 
    #     #     You should not give any information which is not related to the HRMS. 
    #     #     You should not give any information which is not related to the company/organisation/each users. 
    #     #     You should not give any information which is not related to the HRMS. 
    #     #     You should not give any information which is not related to the company/organisation/each users. 
    #     #     You should not give any information which is not related to the HRMS.
    #     prompt = ("""           
    #         Hello there! 👋 I’m your HRMS assistant for effiHR tenants.
    #         I’ve got the info you requested, pulled straight from our documents below.
    #         Please use only what’s provided here—no extra assumptions.
    #         # QUESTION: '{query}'
    #         # PASSAGE: '{relevant_passage}'

    #         ANSWER:
    #     """).format(query=query, relevant_passage=escaped)

    #     return prompt

    def make_prompt(self, query: str, relevant_passage: str | None, role_id: str) -> str:
        if relevant_passage is None:
            relevant_passage = "No relevant passage found."
        
        # Prepare the passage for safe insertion into the prompt string
        escaped_passage = relevant_passage.replace("'", "\\'").replace('"', '\\"').replace("\n", " ")

        # --- Retrieve values from loaded configs ---
        agent_name = agent_profile.get("agent_name")
        org = agent_profile.get("org_name")
        org_description = agent_profile.get("org_description")
        role = agent_profile.get("agent_role")
        tone = agent_profile.get("agent_tone")
        
        objective = agent_prompts.get("agent_objective")
        examples = agent_prompts.get("agent_examples")
        
        target_timezone_str = app_settings.get("default_timezone", "Europe/London") 
        try:
            target_timezone = pytz.timezone(target_timezone_str)
            now_utc = datetime.datetime.now(pytz.utc)
            current_time_in_timezone = now_utc.astimezone(target_timezone)
            
            current_date_time = current_time_in_timezone.strftime("%A, %B %d, %Y at %I:%M:%S %p %Z")
            timezone = target_timezone_str 
        except pytz.UnknownTimeZoneError:
            # Fallback if timezone is invalid
            timezone = "UTC"
            current_date_time = datetime.datetime.now(pytz.utc).strftime("%A, %B %d, %Y at %I:%M:%S %p UTC")

        chat_history = "" # This would be populated with the ongoing conversation history (from your main chat logic)
        user_role = role_id 

        # --- Constructing the Prompt Template from YAML values ---

        # Build the RULES section
        rules_str = "\n".join([f"- {rule}" for rule in agent_prompts.get("rules", [])])

        # Build the EXAMPLE CONVERSATION section
        example_conv_str = ""
        for item in agent_prompts.get("example_conversation", []):
            example_conv_str += f"User: {item.get('user')}\n"
            example_conv_str += f"AI: {item.get('ai')}\n\n"
        example_conv_str = example_conv_str.strip() # Remove trailing newline

        # Build the IMPORTANT section
        important_notes_str = "\n".join([f"- {note}" for note in agent_prompts.get("important_notes", [])])

        # Fill in agent persona prompt variables
        agent_persona_filled = agent_prompts.get("agent_persona_prompt").format(
            agent_name=agent_name,
            agent_role=role, # Use 'role' from agent_profile
            agent_tone=tone, # Use 'tone' from agent_profile
            agent_objective=objective, # Use 'objective' from agent_prompts (which uses agent_profile's data)
            agent_examples=examples # Use 'examples' from agent_prompts (which uses agent_profile's data)
        )

        # Main prompt template structure
        prompt_template = f"""
        # INTENT:
        {agent_prompts.get('system_intent_prefix').format(agent_name=agent_name, org_name=org, org_description=org_description)}

        # RULES:
        {rules_str}

        # AGENT PROMPT
        {agent_persona_filled}

        # EXAMPLE CONVERSATION:
        {example_conv_str}
        # END OF EXAMPLE CONVERSATION

        # INPUTS:
        Timezone: {timezone}
        Current date and time: {current_date_time}
        Current conversation:
        {chat_history}
        User Role: {user_role}
        Human: {query}
        AI:

        # CONTEXTUAL PASSAGE:
        {escaped_passage}

        # IMPORTANT:
        {important_notes_str}
        """
        return prompt_template.format(
            agent_name=agent_name,
            org=org,
            org_description=org_description,
            role=role,
            tone=tone,
            objective=objective,
            examples=examples,
            timezone=timezone,
            current_date_time=current_date_time,
            chat_history=chat_history,
            user_role=user_role,
            query=query,
            escaped_passage=escaped_passage,
        )