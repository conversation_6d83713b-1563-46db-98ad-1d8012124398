
from dataclasses import dataclass
import json
from typing import List, Optional
from typing import Optional
from pydantic import BaseModel
from pypdf import PdfReader
from io import BytesIO
import requests
from docx import Document as DocxDocument
from utils.logger import logger
import os
from dotenv import load_dotenv

load_dotenv()

BASE_URL = os.getenv("BASE_URL", "https://core.effihr.com/internal")
API_KEY  = os.getenv("API_KEY")
ACCEPT_LANGUAGE = os.getenv("ACCEPT_LANGUAGE", "en-IN,en-US;q=0.9,en;q=0.8")
if not API_KEY:
    raise RuntimeError("Missing API_KEY in environment")

class Document(BaseModel):
    tenant_id: str
    role: str
    documents: List[str]

class Documents():
    def __init__(self):
        pass
    
    def get_documents(self):
        url = f"{BASE_URL}/policy/all"
        response = requests.get(url, headers={
            "accept": "application/json, text/plain, */*",
            "accept-language": ACCEPT_LANGUAGE,
            "X-Api-Key": API_KEY,
            })
        
        return response.json().get('response', [])

    def download_document(self, document_url: str) -> bytes | None:
        try:
            response = requests.get(document_url)
            if (response.status_code != 200):
                return None
            if response.status_code == 200:
                return response.content
        except Exception as e:
            return None
        return None

    def generate_embeddings(self, file_content: bytes, file_type: str | None) -> str:
        text = ""
        if file_type == 'pdf':
            # Process PDF content
            pdf_data = BytesIO(file_content)
            reader = PdfReader(pdf_data)
            # Accumulate text from each page
            page_texts = []
            for page in reader.pages:
                extracted_text = page.extract_text()
                if extracted_text:
                    page_texts.append(extracted_text)
            text = "\n".join(page_texts)
        elif file_type == 'docx':
            document = DocxDocument(BytesIO(file_content))
            
            document = DocxDocument(BytesIO(file_content))
            
            full_text = []
            for para in document.paragraphs:
                full_text.append(para.text)
                
            for table in document.tables:
                for row in table.rows:
                    for cell in row.cells:
                        if cell.text not in full_text:
                            full_text.append(cell.text)
            text = "\n".join(full_text)
        else:
            raise ValueError("Unsupported file type")
        
        return text


    def _get_file_type(self, document: str) -> str | None:
        if ".pdf" in document:
            return 'pdf'
        elif ".docx" in document:
            return 'docx'
        else:
            return None