import os
import json
from typing import Generator, List
from dotenv import load_dotenv

import chromadb
from chromadb import Documents, EmbeddingFunction, Embeddings

from google.oauth2.service_account import Credentials
from google import genai
from google.genai import types
from google.genai.types import (
    Part,
    SafetySetting,
    HarmCategory,
    HarmBlockThreshold,
    GenerateContentConfig,
    EmbedContentConfig,
)

# Load environment variables from .env file
load_dotenv()

# Model and embedding configuration from environment or defaults
MODEL_ID = os.getenv("MODEL_ID", "gemini-2.5-flash-preview-04-17")
EMBEDDING_MODEL_ID = os.getenv("EMBEDDING_MODEL_ID", "gemini-embedding-001")
MAX_CACHE_SIZE = int(os.getenv("MAX_CACHE_SIZE", "1024"))

TEMPERATURE = float(os.getenv("TEMPERATURE", "0.45"))
TOP_P = float(os.getenv("TOP_P", "0.1"))

# Google Cloud project and location configuration
GCP_PROJECT_ID = os.getenv("GCP_PROJECT_ID", "")
GCP_LOCATION = os.getenv("GCP_LOCATION", "ap-south-1")
EMBEDDING_LOCATION = os.getenv("EMBEDDING_LOCATION", "global")

# Load service account credentials from environment variable
sa_raw = os.getenv("VERTEX_AI_SA_API")
if not sa_raw:
    raise RuntimeError("Missing VERTEX_AI_SA_API in environment. Please provide your service account JSON.")

try:
    sa_info = json.loads(sa_raw)
except json.JSONDecodeError as e:
    raise ValueError(f"Invalid JSON in VERTEX_AI_SA_API environment variable: {e}")

# Create Google credentials object
creds = Credentials.from_service_account_info(
    sa_info,
    scopes=["https://www.googleapis.com/auth/cloud-platform"],
)

# Initialize the Google GenAI client for text generation
def initialize_gen_client() -> genai.Client:
    if not GCP_PROJECT_ID:
        raise ValueError("GCP_PROJECT_ID is not set. Cannot initialize GenAI client.")
    return genai.Client(
        vertexai=True,
        project=GCP_PROJECT_ID,
        location=GCP_LOCATION,
        credentials=creds,
    )

# Initialize the Google GenAI client for embeddings
def initialize_emb_client() -> genai.Client:
    if not GCP_PROJECT_ID:
        raise ValueError("GCP_PROJECT_ID is not set. Cannot initialize Embedding client.")
    return genai.Client(
        vertexai=True,
        project=GCP_PROJECT_ID,
        location=EMBEDDING_LOCATION,
        credentials=creds,
    )

# Alias for initializing the main client
initialize_client = initialize_gen_client

# Custom embedding function using Gemini embedding model
class GeminiEmbeddingFunction(EmbeddingFunction):
    def __init__(self, model_id: str = EMBEDDING_MODEL_ID):
        self.client = initialize_emb_client()
        self.model_id = model_id

    def __call__(self, inputs: Documents) -> Embeddings:
        # Ensure inputs are a list of strings
        if not isinstance(inputs, list) or not all(isinstance(i, str) for i in inputs):
            raise TypeError("Inputs for embedding must be a list of strings (Documents).")
        try:
            # Call the embedding model
            resp = self.client.models.embed_content(
                model=self.model_id,
                contents=inputs, # type: ignore
                config=EmbedContentConfig(
                    task_type="RETRIEVAL_DOCUMENT",
                    title="Chroma ingestion"
                )
            )
            # Check if embeddings are returned
            if not resp.embeddings:
                raise ValueError("No embeddings returned from the Vertex AI embedding API.")
            for embedding_obj in resp.embeddings:
                if not embedding_obj.values:
                    raise ValueError("An embedding object without values was returned.")
            # Return list of embedding vectors
            return [e.values for e in resp.embeddings] # type: ignore
        except Exception as e:
            print(f"Failed to get embeddings from Vertex AI model '{self.model_id}': {e}")
            raise

# Generator function to stream responses from Gemini model
def generate_stream(
    user_messages: List[str],
    model: str = MODEL_ID,
    temperature: float = TEMPERATURE,
    top_p: float = TOP_P,
    max_output_tokens: int = MAX_CACHE_SIZE,
) -> Generator[str | None, None, None]:
    client = initialize_gen_client()
    # Prepare messages for the model
    contents = [
        types.Content(
            role="user",
            parts=[Part.from_text(msg)] # type: ignore
        )
        for msg in user_messages
    ]
    # Configure generation parameters and safety settings
    cfg = GenerateContentConfig(
        temperature=temperature,
        top_p=top_p,
        max_output_tokens=max_output_tokens,
        safety_settings=[
            SafetySetting(
                category=HarmCategory.HARM_CATEGORY_HATE_SPEECH,
                threshold=HarmBlockThreshold.OFF
            ),
            SafetySetting(
                category=HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
                threshold=HarmBlockThreshold.OFF
            ),
            SafetySetting(
                category=HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
                threshold=HarmBlockThreshold.OFF
            ),
            SafetySetting(
                category=HarmCategory.HARM_CATEGORY_HARASSMENT,
                threshold=HarmBlockThreshold.OFF
            ),
        ],
    )
    # Stream the generated content
    for chunk in client.models.generate_content_stream(
        model=model,
        contents=contents, # type: ignore
        config=cfg,
    ):
        yield chunk.text
