
from contextlib import asynccontextmanager
from dotenv import load_dotenv
from fastapi import FastAPI

from routers.chat import chat_router
from routers.documents import documents_router
from services.service_client import ServiceClient
from services.vectordb import VectorDb
load_dotenv()

@asynccontextmanager
async def lifespan(app: FastAPI):
    vector_storage = VectorDb()
    app.state.vector_storage = vector_storage
    
    yield

app = ServiceClient(lifespan=lifespan).app

app.include_router(chat_router)
app.include_router(documents_router)


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
